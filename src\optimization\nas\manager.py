"""NAS管理器模块 - 统一管理神经架构搜索流程

模块路径: src/optimization/nas/manager.py

功能说明：
1. NASManager: 搜索流程总控制器
2. NASConfig: 搜索配置管理
3. SearchResult: 搜索结果封装
4. 集成到GANTrainer训练流程
"""

from __future__ import annotations

import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import torch
from torch.utils.data import DataLoader

from src.models.gan.gan_model import GANModel
from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger

from .evaluator import EvaluationResult, NASEvaluator
from .search_space import ArchitectureConfig, SearchSpaceManager
from .searchers import DARTSSearcher, EvolutionarySearcher, SearcherBase
from .utils import ArchitectureEncoder, ResourceMonitor, SearchLogger


@dataclass
class NASConfig:
    """NAS搜索配置"""
    # 搜索策略配置
    search_strategy: str = "evolutionary"  # "darts" 或 "evolutionary"
    max_iterations: int = 50
    time_budget_hours: float = 24.0

    # 评估配置
    max_epochs_per_eval: int = 10
    early_stop_patience: int = 3
    memory_limit_mb: float = 2800

    # 进化算法配置
    population_size: int = 20
    mutation_rate: float = 0.3
    crossover_rate: float = 0.7

    # DARTS配置
    darts_learning_rate: float = 0.025
    darts_momentum: float = 0.9

    # 资源约束
    gpu_memory_limit_gb: float = 3.0
    enable_distributed: bool = False
    num_workers: int = 1

    # 日志和保存
    log_dir: str = "logs/nas"
    save_dir: str = "outputs/nas"
    experiment_name: str = "gan_nas_search"
    save_intermediate_results: bool = True

    def __post_init__(self):
        """验证配置"""
        if self.search_strategy not in ["darts", "evolutionary"]:
            raise ValueError(f"不支持的搜索策略: {self.search_strategy}")

        if self.max_iterations <= 0:
            raise ValueError("max_iterations必须大于0")

        if self.time_budget_hours <= 0:
            raise ValueError("time_budget_hours必须大于0")


@dataclass
class SearchResult:
    """搜索结果"""
    best_architecture: Optional[ArchitectureConfig] = None
    best_metrics: Optional[Dict[str, float]] = None
    search_history: List[EvaluationResult] = field(default_factory=list)
    total_iterations: int = 0
    total_time_hours: float = 0.0
    success: bool = False
    error_message: str = ""

    def get_best_mae(self) -> float:
        """获取最佳MAE分数"""
        if not self.best_metrics:
            raise RuntimeError("没有可用的最佳指标")
        if 'mae_score' not in self.best_metrics:
            raise RuntimeError("最佳指标中缺少mae_score")
        return self.best_metrics['mae_score']

    def get_improvement_over_baseline(self, baseline_mae: float) -> float:
        """计算相对于基线的改进"""
        if baseline_mae <= 0:
            raise ValueError(f"基线MAE必须为正数: {baseline_mae}")

        best_mae = self.get_best_mae()
        return (baseline_mae - best_mae) / baseline_mae * 100


class NASManager:
    """神经架构搜索管理器"""

    def __init__(self,
                 base_config: ConfigManager,
                 nas_config: NASConfig,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 baseline_mae: Optional[float] = None,
                 target_standardizer=None):
        """
        Args:
            base_config: 基础配置管理器
            nas_config: NAS搜索配置
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            baseline_mae: 基线MAE分数 (用于比较改进)
            target_standardizer: 目标标准化器 (用于反标准化MAE计算)
        """
        self.logger = get_logger("NASManager")
        self.base_config = base_config
        self.nas_config = nas_config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.baseline_mae = baseline_mae
        self.target_standardizer = target_standardizer

        # 初始化组件
        self.search_space = SearchSpaceManager(
            memory_limit_gb=nas_config.gpu_memory_limit_gb
        )

        # 初始化评估器 (传递target_standardizer)
        self.evaluator = NASEvaluator(
            config=base_config,
            train_loader=train_loader,
            val_loader=val_loader,
            max_epochs=nas_config.max_epochs_per_eval,
            early_stop_patience=nas_config.early_stop_patience,
            memory_limit_mb=nas_config.memory_limit_mb,
            target_standardizer=target_standardizer
        )

        # 记录标准化器状态
        if target_standardizer is not None:
            self.logger.info("NAS管理器已设置target_standardizer，将同时计算标准化和反标准化MAE")
        else:
            self.logger.info("NAS管理器未设置target_standardizer，将只计算标准化空间MAE用于评分")

        self.resource_monitor = ResourceMonitor()
        self.architecture_encoder = ArchitectureEncoder()

        # 创建日志和保存目录
        self.log_dir = Path(nas_config.log_dir)
        self.save_dir = Path(nas_config.save_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.save_dir.mkdir(parents=True, exist_ok=True)

        self.search_logger = SearchLogger(
            log_dir=self.log_dir,
            experiment_name=nas_config.experiment_name
        )

        self.logger.info(f"NAS管理器初始化完成 - 策略: {nas_config.search_strategy}, "
                        f"最大迭代: {nas_config.max_iterations}, "
                        f"时间预算: {nas_config.time_budget_hours}小时")

    def run_search(self) -> SearchResult:
        """运行架构搜索"""
        start_time = time.time()
        self.logger.info("开始神经架构搜索")

        try:
            # 记录搜索配置
            self.search_logger.set_metadata('nas_config', self.nas_config.__dict__)
            self.search_logger.set_metadata('baseline_mae', self.baseline_mae)

            # 创建搜索器
            searcher = self._create_searcher()

            # 执行搜索
            best_result = searcher.search()

            # 处理搜索结果
            search_result = self._process_search_result(
                best_result=best_result,
                search_history=searcher.search_history,
                total_time=time.time() - start_time
            )

            # 保存结果
            self._save_search_result(search_result)

            # 记录搜索完成
            self.search_logger.finalize_search(
                total_iterations=len(searcher.search_history),
                best_architecture=search_result.best_architecture,
                best_metrics=search_result.best_metrics
            )

            self.logger.info(f"架构搜索完成 - 最佳MAE: {search_result.get_best_mae():.4f}, "
                           f"耗时: {search_result.total_time_hours:.2f}小时")

            return search_result

        except Exception as e:
            error_msg = f"架构搜索失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return SearchResult(
                success=False,
                error_message=error_msg,
                total_time_hours=(time.time() - start_time) / 3600
            )

    def _create_searcher(self) -> SearcherBase:
        """创建搜索器"""
        if self.nas_config.search_strategy == "darts":
            return DARTSSearcher(
                search_space=self.search_space,
                evaluator=self.evaluator,
                max_iterations=self.nas_config.max_iterations,
                time_budget_hours=self.nas_config.time_budget_hours,
                learning_rate=self.nas_config.darts_learning_rate,
                momentum=self.nas_config.darts_momentum
            )
        elif self.nas_config.search_strategy == "evolutionary":
            return EvolutionarySearcher(
                search_space=self.search_space,
                evaluator=self.evaluator,
                max_iterations=self.nas_config.max_iterations,
                time_budget_hours=self.nas_config.time_budget_hours,
                population_size=self.nas_config.population_size,
                mutation_rate=self.nas_config.mutation_rate,
                crossover_rate=self.nas_config.crossover_rate
            )
        else:
            raise ValueError(f"不支持的搜索策略: {self.nas_config.search_strategy}")

    def _process_search_result(self,
                              best_result: EvaluationResult,
                              search_history: List[EvaluationResult],
                              total_time: float) -> SearchResult:
        """处理搜索结果"""
        if best_result and best_result.success:
            best_metrics = {
                'mae_score': best_result.metrics.mae_score,
                'training_stability': best_result.metrics.training_stability,
                'inference_speed': best_result.metrics.inference_speed,
                'parameter_count': best_result.metrics.parameter_count,
                'memory_usage_mb': best_result.metrics.memory_usage_mb,
                'overall_score': best_result.metrics.overall_score
            }

            return SearchResult(
                best_architecture=best_result.architecture,
                best_metrics=best_metrics,
                search_history=search_history,
                total_iterations=len(search_history),
                total_time_hours=total_time / 3600,
                success=True
            )
        else:
            return SearchResult(
                search_history=search_history,
                total_iterations=len(search_history),
                total_time_hours=total_time / 3600,
                success=False,
                error_message="未找到有效的架构"
            )

    def _save_search_result(self, result: SearchResult):
        """保存搜索结果"""
        try:
            # 保存最佳架构配置
            if result.best_architecture:
                best_arch_file = self.save_dir / "best_architecture.json"
                self.architecture_encoder.save_to_file(result.best_architecture, best_arch_file)

            # 保存搜索摘要
            summary_file = self.save_dir / "search_summary.json"
            summary_data = {
                'success': result.success,
                'total_iterations': result.total_iterations,
                'total_time_hours': result.total_time_hours,
                'best_metrics': result.best_metrics,
                'baseline_comparison': {
                    'baseline_mae': self.baseline_mae,
                    'improvement_percent': result.get_improvement_over_baseline(self.baseline_mae) if self.baseline_mae else None
                },
                'search_config': self.nas_config.__dict__
            }

            import json
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"搜索结果已保存到: {self.save_dir}")

        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")

    def create_optimized_model(self, architecture: ArchitectureConfig) -> Optional[GANModel]:
        """根据搜索到的架构创建优化模型"""
        try:
            # 创建临时配置
            temp_config = self._create_optimized_config(architecture)

            # 获取特征维度
            feature_dim = self._get_feature_dim_from_data()

            # 创建模型
            model = GANModel(
                config=temp_config,
                feature_dim=feature_dim,
                window_size=self.base_config.data.window_size
            )

            self.logger.info(f"基于搜索架构创建优化模型成功 - "
                           f"参数数量: {sum(p.numel() for p in model.parameters()):,}")

            return model

        except Exception as e:
            self.logger.error(f"创建优化模型失败: {e}")
            return None

    def _create_optimized_config(self, architecture: ArchitectureConfig) -> ConfigManager:
        """根据架构创建优化配置"""
        # 复制基础配置
        optimized_config = ConfigManager.from_config(self.base_config)

        # 应用搜索到的架构参数
        gen_config = architecture.generator
        disc_config = architecture.discriminator
        encoder_config = architecture.feature_encoder

        # 安全地更新配置属性
        try:
            optimized_config.model.hidden_dim = gen_config.hidden_dim
            optimized_config.model.dropout_rate = gen_config.dropout_rate
        except Exception as e:
            self.logger.warning(f"无法更新基础模型配置: {e}")

        # 更新生成器配置 - 使用字典方式
        try:
            if hasattr(optimized_config.model, 'generator') and optimized_config.model.generator:
                if isinstance(optimized_config.model.generator, dict):
                    optimized_config.model.generator['num_layers'] = gen_config.num_layers
                else:
                    optimized_config.model.generator.num_layers = gen_config.num_layers
        except Exception as e:
            self.logger.warning(f"无法更新生成器配置: {e}")

        # 更新判别器配置 - 使用字典方式
        try:
            if hasattr(optimized_config.model, 'discriminator') and optimized_config.model.discriminator:
                if isinstance(optimized_config.model.discriminator, dict):
                    optimized_config.model.discriminator['hidden_dim'] = disc_config.hidden_dim
                    optimized_config.model.discriminator['num_layers'] = disc_config.num_layers
                else:
                    optimized_config.model.discriminator.hidden_dim = disc_config.hidden_dim
                    optimized_config.model.discriminator.num_layers = disc_config.num_layers
        except Exception as e:
            self.logger.warning(f"无法更新判别器配置: {e}")

        # 更新注意力配置 - 使用字典方式
        try:
            if hasattr(optimized_config.model, 'attention') and optimized_config.model.attention:
                if isinstance(optimized_config.model.attention, dict):
                    optimized_config.model.attention['multi_head_num_heads'] = gen_config.num_attention_heads
                    optimized_config.model.attention['multi_head_dropout'] = gen_config.dropout_rate
                else:
                    optimized_config.model.attention.multi_head_num_heads = gen_config.num_attention_heads
                    optimized_config.model.attention.multi_head_dropout = gen_config.dropout_rate
        except Exception as e:
            self.logger.warning(f"无法更新注意力配置: {e}")

        # 更新特征提取器配置 - 使用字典方式
        try:
            if hasattr(optimized_config.model, 'feature_extractor') and optimized_config.model.feature_extractor:
                if isinstance(optimized_config.model.feature_extractor, dict):
                    optimized_config.model.feature_extractor['msfe_num_scales'] = encoder_config.num_scales
                    optimized_config.model.feature_extractor['msfe_hidden_dim'] = gen_config.hidden_dim

                    # 处理卷积核大小列表
                    if hasattr(encoder_config, 'conv_kernel_sizes') and encoder_config.conv_kernel_sizes:
                        optimized_config.model.feature_extractor['msfe_kernel_sizes'] = encoder_config.conv_kernel_sizes
                else:
                    optimized_config.model.feature_extractor.msfe_num_scales = encoder_config.num_scales
                    optimized_config.model.feature_extractor.msfe_hidden_dim = gen_config.hidden_dim

                    # 处理卷积核大小列表
                    if hasattr(encoder_config, 'conv_kernel_sizes') and encoder_config.conv_kernel_sizes:
                        optimized_config.model.feature_extractor.msfe_kernel_sizes = encoder_config.conv_kernel_sizes
        except Exception as e:
            self.logger.warning(f"无法更新特征提取器配置: {e}")

        return optimized_config

    def _get_feature_dim_from_data(self) -> int:
        """从数据中获取特征维度"""
        try:
            batch = next(iter(self.train_loader))
            features = batch['features']
            return features.shape[-1]
        except Exception as e:
            self.logger.warning(f"无法从数据获取特征维度: {e}, 使用默认值19")
            return 19

    def load_best_architecture(self) -> Optional[ArchitectureConfig]:
        """加载最佳架构配置"""
        best_arch_file = self.save_dir / "best_architecture.json"
        if best_arch_file.exists():
            return self.architecture_encoder.load_from_file(best_arch_file)
        return None

    def get_search_summary(self) -> Dict[str, Any]:
        """获取搜索摘要"""
        return self.search_logger.get_search_summary()
