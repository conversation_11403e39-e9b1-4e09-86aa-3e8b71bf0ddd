# NAS专用配置文件
# 针对神经架构搜索优化的配置参数

version: "2.0.0"

# 数据配置 (快速基线训练优化)
data:
  raw_data_path: "data/raw/combined_data.csv"
  target_column: "value15"
  feature_columns: ["value1", "value2", "value3", "value4", "value5", "value6", "value7", "value8", "value9", "value10", "value11", "value12", "value13", "value14", "value16", "value17", "value18", "value19", "value20"]
  window_size: 24         # 快速: 从7增加到24 (更合理的窗口大小)
  prediction_horizon: 1
  train_ratio: 0.7
  val_ratio: 0.15
  test_ratio: 0.15
  normalize: true
  remove_outliers: true

# 模型配置 (快速基线配置，将被NAS优化)
model:
  # 基础配置 (协调减少参数)
  hidden_dim: 32          # 从原32保持，协调优化
  noise_dim: 32           # 从原32保持，协调优化
  dropout_rate: 0.05      # 保持较低dropout加速收敛

  # 生成器配置 (协调优化)
  generator:
    num_layers: 2         # 与主配置协调一致
    use_layer_norm: true
    use_residual: true
    activation: "relu"

  # 判别器配置 (协调优化)
  discriminator:
    hidden_dim: 32        # 保持32，协调优化
    num_layers: 3         # 从4降到3，协调优化
    enable_dynamic_fusion: false       # 关闭动态特征融合
    enable_temporal_attention: false   # 关闭时序多头注意力
    enable_adaptive_dilation_attention: false # 关闭自适应扩张率注意力
    enable_multiscale_convolution_attention: false # 关闭多尺度卷积注意力
    use_spectral_norm: true # 保留稳定性
    branch_config:
      trend_branch: false    # 关闭趋势一致性分支
      feature_branch: false  # 关闭特征关联分支
      temporal_branch: false # 关闭时序模式分支

  # 注意力机制配置 (协调优化)
  attention:
    multi_head_num_heads: 2   # 保持2头
    multi_head_dropout: 0.05
    multi_scale_num_heads: 2  # 保持2头
    multi_scale_num_scales: 1 # 保持1尺度
    multi_scale_dilation_rates: [4] # 保持单个值
    multi_scale_dropout: 0.05
    temporal_wrapper_dropout: 0.05
    adaptive_num_heads: 2     # 保持2头
    adaptive_num_scales: 2    # 保持2尺度，协调优化
    adaptive_dropout: 0.05

  # 特征提取器配置 (协调优化)
  feature_extractor:
    msfe_num_scales: 2        # 保持2尺度，协调优化
    msfe_dropout: 0.05
    msfe_kernel_sizes: [3, 5] # 保持[3,5]，协调优化
    msfe_hidden_dim: 32       # 保持32，协调优化
    tsfe_num_layers: 2        # 保持最小值
    tsfe_dropout: 0.05

# 训练配置 (快速基线训练优化)
training:
  batch_size: 32          # 保持: 小批次快速迭代
  num_epochs: 10          # NAS环境中的快速训练轮次
  lambda_gp: 0.05         # 梯度惩罚权重
  use_adaptive_lambda_gp: true
  num_workers: 0
  dropout_rate: 0.1
  seed: 42
  save_dir: outputs/nas_models

  # 优化器配置
  optimizer:
    type: adam
    generator_lr: 0.002   # 快速: 从0.001提高到0.002 (加速收敛)
    discriminator_lr: 0.002
    weight_decay: 0.0001  # 保持: 轻微正则化
    beta1: 0.5
    beta2: 0.999
    momentum: 0.9
    nesterov: false
    eps: 1e-8

  # 学习率调度器配置
  lr_scheduler:
    enabled: true
    factor: 0.8           # 快速: 从0.5提高到0.8 (更温和的衰减)
    patience: 1           # 快速: 从5降到1 (更快响应)
    min_delta: 1e-4
    monitor: val_loss

  # 早停配置 (快速优化)
  early_stopping:
    enabled: true
    patience: 2           # 快速: 从10降到2 (更快停止)
    min_delta: 0.001      # 快速: 从0.0001放宽到0.001
    monitor: "val_mae"    # 保持: 监控验证MAE
    mode: "min"

  # 检查点配置
  checkpoint:
    enable_checkpointing: true
    metric_name: "val_mae"
    metric_mode: "min"
    keep_best_k: 2        # NAS环境中减少保存数量
    save_freq: 5          # 减少保存频率
    keep_last_n: 1
    memory_optimization: true

  # 批次大小优化器配置
  batch_size_optimizer:
    enabled: false        # NAS环境中禁用以简化
    initial_batch_size: 32
    min_batch_size: 16
    max_batch_size: 128
    memory_utilization_target: 0.8
    strategy: conservative
    adjustment_interval: 50
    growth_factor: 1.1
    shrink_factor: 0.9
    stability_threshold: 3
    warmup_steps: 50
    oom_recovery: true
    step_size: 16
    patience: 2
    monitor: val_loss

  # 学习率平衡器配置 (新增)
  lr_balancer:
    enabled: true
    type: "enhanced_multi_metric"
    target_ratio: 1.2
    sensitivity: 0.15
    min_lr: 0.0001        # 快速: 从0.00001提高到0.0001
    max_lr: 0.004
    epsilon: 0.00000001
    performance_weight: 0.4
    stability_weight: 0.2
    loss_ratio_weight: 0.4
    history_window: 3     # NAS环境中减少历史窗口
    improvement_threshold: 0.05
    stability_threshold: 0.15
    # 调整模式配置
    adjustment_mode: "adaptive"  # "sync", "inverse", "adaptive"
    mode_switch_threshold: 0.3   # 自适应模式切换阈值

  # 训练平衡配置
  balance:
    lower_threshold: 0.4
    upper_threshold: 1.0
    min_n_critic: 1
    max_n_critic: 2       # NAS环境中减少最大critic步数
    min_g_steps: 1
    max_g_steps: 3        # NAS环境中减少最大生成器步数

  # 动态批次大小
  dynamic_batch_size: false  # NAS环境中禁用
  gradient_explosion_threshold: 5.0
  gradient_clip_val: 0.5     # 快速: 从1.0降到0.5 (更严格的梯度裁剪)

  # 自适应梯度惩罚配置
  adaptive_lambda_gp:
    enabled: true
    base_lambda_gp: 0.05
    min_lambda_gp: 0.01
    max_lambda_gp: 0.5     # NAS环境中降低最大值
    adaptation_rate: 0.15
    warmup_steps: 3        # NAS环境中减少预热步数
    update_interval: 1     # NAS环境中更频繁更新
    smoothing_factor: 0.7
    grad_norm_target: 1.0
    grad_norm_tolerance: 0.25
    verbose_logging: false # NAS环境中减少日志

  # 混合精度配置
  mixed_precision:
    enabled: false         # NAS环境中禁用以简化
    dtype: "float16"
    init_scale: 65536.0
    growth_factor: 2.0
    backoff_factor: 0.5
    growth_interval: 2000
    cast_model_outputs: false

# 模型配置 (NAS优化)
model:
  type: "gan"
  noise_dim: 64
  dimensions:
    base_dim: 64
  noise:
    dim: 64
    distribution: "normal"
    scale: 1.0
    seed: 42
    dtype: "float32"
    structured: true
    temporal_correlation: 0.5
    feature_correlation: 0.3
    noise_patterns: ["temporal", "feature", "pattern"]

# GAN特定训练配置 (快速优化)
gan_training:
  # 训练平衡参数 (快速优化)
  n_critic: 1             # 保持: 最小critic步数
  g_steps: 1              # 保持: 最小生成器步数
  max_n_critic: 3         # 快速: 从10降到3 (减少最大critic步数)
  min_n_critic: 1         # 保持: 最小critic步数

  # 梯度惩罚 (快速优化)
  lambda_gp: 0.05         # 快速: 从0.0提高到0.05 (轻微梯度惩罚)
  adaptive_lambda_gp:
    enabled: true         # 保留: 自适应调整
    min_lambda: 0.0       # 保持: 最小值
    max_lambda: 0.5       # 快速: 从10.0降到0.5 (降低最大值)
    adjustment_frequency: 1 # 快速: 从2降到1 (更频繁调整)
    warmup_steps: 3       # 快速: 从5降到3 (减少预热步数)

  # 损失权重 (快速优化)
  adversarial_weight: 1.0 # 保持: 对抗损失权重
  regression_weight: 5.0  # 快速: 从10.0降到5.0 (降低回归权重)

  # 训练稳定性 (保留)
  spectral_norm: true     # 保留: 有助于训练稳定性
  gradient_penalty: true  # 保留: 有助于训练稳定性

# 路径配置 (NAS优化)
paths:
  data_dir: "data"
  model_dir: "models"
  raw_data: "data/raw/combined_data.csv"
  checkpoint_dir: "checkpoints"
  logs_dir: "logs"
  results_dir: "outputs/nas_results"
  model_files:
    generator: "nas_generator.pt"
    discriminator: "nas_discriminator.pt"

# 系统配置 (NAS优化)
system:
  device: "cuda"
  cuda:
    enabled: true           # 保留: 使用GPU加速
    device_id: 0            # 保持: 使用第一个GPU
    memory_fraction: 0.9    # NAS环境中使用更多GPU内存
  memory:
    memory_limit: 0.9
    monitoring_interval: 60
  cache:
    enable_memory_cache: true
    size: 200

# 日志配置 (快速优化)
logging:
  level: "INFO"           # 保持: INFO级别 (避免过多DEBUG信息)
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  file:
    enabled: true
    path: "logs/nas_experiment.log"
    max_size: 10485760
    backup_count: 5
  console:
    enabled: true
    level: "INFO"
  save_history: true
  metrics: ["loss", "val_loss", "mse", "mae", "rmse"]
  module_levels:
    src.models.gan: "INFO"
    src.data: "INFO"
    src.utils: "INFO"
  performance_monitoring:
    enable: true
    cuda_tracking: true
    memory_tracking: true
  handlers:
    console:
      enabled: true
      level: "INFO"
    file:
      enabled: true
      level: "INFO"

# 监控配置 (NAS优化)
monitoring:
  enable_model_stats: false  # 关闭模型统计监控
  enable_error_monitor: false # 关闭错误监控
  stats_log_frequency: 20   # NAS环境中适中频率 (避免过多日志)
  stats_detailed_frequency: 100
  stats_log_path: logs/nas_model_stats.log
  track_weights: false       # 关闭权重跟踪
  track_gradients: false    # 关闭梯度跟踪
  track_activations: false  # 关闭激活跟踪
  nan_detection: false       # 关闭NaN检测
  error_log_path: logs/nas_error_monitor.log
  enable_nan_detection_in_error_monitor: false  # 关闭错误监控中的NaN检测
  enable_traceback: false    # 关闭错误回溯
  max_errors: 10            # NAS环境中允许更多错误 (搜索过程中可能出现)
  error_cooldown: 120       # NAS环境中延长冷却时间

# 特征工程配置 (NAS优化)
feature_engineering:
  enable: false  # NAS环境中禁用特征工程以提高速度

# 评估配置 (NAS优化)
evaluation:
  metrics: ["mae"]  # 保留最小评估指标以满足验证要求

# 预处理配置 (NAS优化)
preprocessing: {}

# 特征选择配置 (NAS优化)
feature_selection:
  enable: false  # NAS环境中禁用特征选择以提高速度

# 预测配置 (NAS优化)
prediction:
  batch_size: 64
  device: "cuda"

# NAS特定配置
nas:
  # 搜索策略
  search_strategy: "evolutionary"  # "evolutionary" 或 "darts"

  # 搜索预算
  max_iterations: 30
  time_budget_hours: 8.0

  # 评估配置 (快速优化)
  max_epochs_per_eval: 3  # 快速: 从5降到3 (更快评估)
  early_stop_patience: 1  # 快速: 从2降到1 (更快停止)
  memory_limit_mb: 2800   # 保持: 内存限制

  # 进化算法配置
  evolutionary:
    population_size: 15
    mutation_rate: 0.3
    crossover_rate: 0.7
    elite_ratio: 0.1

  # DARTS配置
  darts:
    learning_rate: 0.025
    momentum: 0.9
    weight_decay: 0.0003
    temperature: 1.0

  # 搜索空间配置
  search_space:
    # 生成器搜索空间 (快速优化)
    generator:
      num_layers_range: [2, 3]     # 保持[2,3]
      hidden_dim_options: [32, 64]
      attention_heads_options: [2] # 从[2,4]缩小到[2]
      dropout_range: [0.0, 0.1]
      activation_types: ["relu", "leaky_relu"]

    # 判别器搜索空间 (快速优化)
    discriminator:
      num_layers_range: [3, 4]     # 快速: 从[3,6]缩小到[3,4]
      hidden_dim_options: [32, 64] # 快速: 从[96,192,384]缩小到[32,64]
      branch_combinations: ["trend_feature", "feature_temporal"] # 快速: 只保留最有效的组合
      attention_mechanisms: ["multi_head", "temporal_wrapper"] # 快速: 只保留最有效的机制

    # 特征编码器搜索空间 (快速优化)
    feature_encoder:
      num_scales_range: [1, 2]     # 从[2,3]缩小到[1,2]
      kernel_size_options: [3, 5]
      window_size_options: [12, 24]
      amplification_factor_range: [1.0, 2.0]
      fusion_types: ["dynamic", "attention"]

  # 资源约束
  constraints:
    gpu_memory_limit_gb: 3.0
    max_parameters: 10000000  # 1000万参数限制
    min_inference_speed: 50   # 最小推理速度 (samples/sec)

  # 输出配置
  output:
    log_dir: "logs/nas"
    save_dir: "outputs/nas"
    save_intermediate_results: true
    save_search_history: true

# 实验配置
experiment:
  name: "gan_timeseries_nas"
  description: "多通道时序预测GAN的神经架构搜索"
  tags: ["nas", "gan", "timeseries", "value15_prediction"]

  # 基线对比 (快速优化)
  baseline:
    enabled: true
    epochs: 3             # 快速: 从5降到3 (更快的基线训练)
    save_model: true      # 保留: 保存基线模型

  # 结果分析
  analysis:
    generate_plots: true
    save_metrics: true
    compare_architectures: true

# 分布式配置 (可选)
distributed:
  enabled: false
  num_workers: 1
  backend: "nccl"

# 调试配置
debug:
  enabled: false
  save_intermediate_models: false
  detailed_logging: false
  memory_profiling: false
